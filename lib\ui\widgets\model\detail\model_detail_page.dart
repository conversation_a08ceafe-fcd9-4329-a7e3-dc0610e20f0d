// {{ AURA-X: Add - 创建模型详细信息页面. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../shared/model_utils.dart';
import 'model_detail_card.dart';

class ModelDetailPage extends StatefulWidget {
  final ProfileController controller;

  const ModelDetailPage({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<ModelDetailPage> createState() => _ModelDetailPageState();
}

class _ModelDetailPageState extends State<ModelDetailPage> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializePageController();
  }

  void _initializePageController() {
    // 找到当前选中模型的索引
    final currentModelName = widget.controller.currentModelName.value;
    _currentIndex = widget.controller.models.indexWhere(
      (model) => ModelUtils.getModelName(model) == currentModelName,
    );
    if (_currentIndex == -1) _currentIndex = 0;

    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: _buildAppBar(context),
      body: Obx(() {
        if (widget.controller.models.isEmpty) {
          return _buildLoadingState();
        }

        return Stack(
          children: [
            // 模型详细信息 - 全屏显示
            PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: widget.controller.models.length,
              itemBuilder: (context, index) {
                final model = widget.controller.models[index];
                return ModelDetailCard(
                  model: model,
                  controller: widget.controller,
                );
              },
            ),

            // 底部导航控制器
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomNavigator(context),
            ),
          ],
        );
      }),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        '模型详细信息',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        // 当前模型指示
        Obx(() {
          if (widget.controller.models.isEmpty) return const SizedBox();
          
          final currentModel = widget.controller.models[_currentIndex];
          final isSelected = ModelUtils.isCurrentModel(
            currentModel,
            widget.controller.currentModelName.value,
          );
          
          if (isSelected) {
            return Container(
              margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Text(
                '当前使用',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }
          
          return const SizedBox();
        }),
      ],
    );
  }

  Widget _buildBottomNavigator(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左箭头
          _buildNavigationButton(
            context,
            Icons.chevron_left,
            _currentIndex > 0,
            _previousPage,
          ),

          // 页面指示器和模型信息
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 页面指示器点
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    widget.controller.models.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 3),
                      width: index == _currentIndex ? 12 : 6,
                      height: 6,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        color: index == _currentIndex
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // 当前模型名称
                if (widget.controller.models.isNotEmpty)
                  Text(
                    ModelUtils.getModelName(widget.controller.models[_currentIndex]),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),

          // 右箭头
          _buildNavigationButton(
            context,
            Icons.chevron_right,
            _currentIndex < widget.controller.models.length - 1,
            _nextPage,
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton(
    BuildContext context,
    IconData icon,
    bool enabled,
    VoidCallback? onPressed,
  ) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: enabled
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(22),
      ),
      child: IconButton(
        onPressed: enabled ? onPressed : null,
        icon: Icon(
          icon,
          color: enabled
              ? Theme.of(context).primaryColor
              : Colors.grey.shade400,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            '正在加载模型详细信息...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _previousPage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPage() {
    if (_currentIndex < widget.controller.models.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}
