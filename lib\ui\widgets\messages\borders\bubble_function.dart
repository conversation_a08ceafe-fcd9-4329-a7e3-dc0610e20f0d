import 'package:aichat/data/const.dart';
import 'package:aichat/ui/theme/chat_theme.dart';
import 'package:aichat/ui/widgets/messages/custom/rilakkuma_markdown_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';

import '../../../../core/controllers/character_controller.dart';
import '../../../../core/controllers/messages_controller.dart';
import '../pull_down_buttom.dart';
import 'bubble_widget.dart';

class BubbleFunction extends StatefulWidget {
  final MessageModel message;
  final String? avatar;

  const BubbleFunction(this.avatar, {super.key, required this.message});

  static Widget customMessageBubble(MessageModel message) {
    // 所有内容转移到了其他文件内
    BubbleFunction bubbleFunction = BubbleFunction(
      message.avatar,
      message: message,
    );
    return bubbleFunction;
  }

  @override
  State<BubbleFunction> createState() => _BubbleFunction();
}

class _BubbleFunction extends State<BubbleFunction>
    with TickerProviderStateMixin {
  // 获取角色控制器
  CharacterController controller = Get.find();

  // {{ AURA-X: Add - 添加消息控制器以支持流式状态检测. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  // 获取消息控制器
  MessagesController messagesController = Get.find();

  // 当前的主题
  ChatThemeConfig? themeConfig;

  // {{ AURA-X: Add - 添加打字机光标动画控制器. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  // 打字机光标动画控制器
  late AnimationController _cursorAnimationController;
  late Animation<double> _cursorAnimation;

  @override
  void initState() {
    super.initState();
    // 从本地读取对话信息界面的主题信息
    _loadTheme();

    // {{ AURA-X: Fix - 初始化打字机光标动画，但不立即启动. Approval: 寸止(ID:2025-08-04T21:10:56+08:00). }}
    // 初始化打字机光标动画
    _cursorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cursorAnimationController,
      curve: Curves.easeInOut,
    ));

    // {{ AURA-X: Fix - 不在初始化时启动动画，而是根据流式状态动态控制. Approval: 寸止(ID:2025-08-04T21:10:56+08:00). }}
    // 动画将在需要时通过_updateAnimationState()方法启动
  }

  void _loadTheme() async {
    final config = await ChatThemeManager.loadThemeForCharacter(
        controller.currentCharacterId.value);
    // mounted：检测该界面是否还存在
    if (mounted) {
      // 涉及到显示的数据赋值，都需要调用到setState才有效
      setState(() {
        themeConfig = config;
      });
    }
  }

  // {{ AURA-X: Add - 动态控制动画状态. Approval: 寸止(ID:2025-08-04T21:10:56+08:00). }}
  void _updateAnimationState(bool shouldAnimate) {
    if (shouldAnimate) {
      // 如果需要动画且当前没有运行，则启动动画
      if (!_cursorAnimationController.isAnimating) {
        _cursorAnimationController.repeat(reverse: true);
      }
    } else {
      // 如果不需要动画，则停止动画
      if (_cursorAnimationController.isAnimating) {
        _cursorAnimationController.stop();
        _cursorAnimationController.reset();
      }
    }
  }

  // {{ AURA-X: Add - 添加dispose方法清理动画资源. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  @override
  void dispose() {
    _cursorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (themeConfig == null) {
      return const SizedBox.shrink();
    }

    // {{ AURA-X: Fix - 简化流式内容显示逻辑，直接使用message.content. Approval: 寸止(ID:2025-08-04T22:01:59+08:00). }}
    // 使用Obx包装以响应流式状态变化
    return Obx(() {
      // 检测是否为当前流式消息
      bool isCurrentStreamingMessage =
          messagesController.currentStreamingMessage.value?.id ==
              widget.message.id;
      bool isStreaming =
          messagesController.isStreaming.value && isCurrentStreamingMessage;

      // 只有当前流式消息且是AI消息时才需要动画
      bool shouldAnimate = isStreaming && widget.message.ownerType == OwnerType.receiver;
      _updateAnimationState(shouldAnimate);

      // {{ AURA-X: Fix - 简化文本获取逻辑，直接使用message.content. Approval: 寸止(ID:2025-08-04T22:01:59+08:00). }}
      // 由于新架构直接更新MessageModel.content，这里统一从message.content获取内容
      String text = widget.message.content?.toString() ?? '';

      return _buildMessageBubble(context, text, isStreaming);
    });
  }

  // {{ AURA-X: Add - 构建消息气泡的核心方法. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  Widget _buildMessageBubble(
      BuildContext context, String text, bool isStreaming) {
    PullDown pullDown = PullDown(
      message: widget.message,
      context: context,
    );

    // 获取当前的主题颜色
    final colorScheme = Theme.of(context).colorScheme;

    // 使用主题配置中的样式设置，而不是硬编码的条件判断
    final receiverColor = themeConfig!.receiverBubbleColor;
    final senderColor = themeConfig!.senderBubbleColor;
    final bubbleRadius = themeConfig!.bubbleRadius;
    final fontFamily = themeConfig!.fontFamily;
    final receiverTextColor = themeConfig!.receiverTextColor;
    final senderTextColor = themeConfig!.senderTextColor;
    final fontSize = themeConfig!.fontSize;

    // 获取主题配置的阴影设置
    final bubbleShadows = themeConfig!.bubbleShadows ??
        [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];

    final avatarShadows = themeConfig!.avatarShadows ??
        [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];

    return GestureDetector(
      onTapDown: (details) async {
        var potion = details.globalPosition & Size.zero;
        // 通过点击位置的globalPosition全局位置 + 数值为0的组件尺寸大小，组成了Rect类型的数据
        await pullDown.showPullDown(potion);
      },
      child: Row(
        // 根据ai还是用户来显示气泡位置
        mainAxisAlignment: (widget.message.ownerType == OwnerType.receiver)
            ? MainAxisAlignment.start
            : MainAxisAlignment.end,
        // 次轴用于对其头像在左上角的位置
        crossAxisAlignment: (widget.message.ownerType == OwnerType.receiver)
            ? CrossAxisAlignment.start
            : CrossAxisAlignment.center,

        children: [
          // 显示ai头像
          if ((widget.message.ownerType == OwnerType.receiver) &&
              widget.avatar != null)
            Padding(
              padding: const EdgeInsets.fromLTRB(5, 10, 0, 0),
              child: ClipOval(
                child: Container(
                  decoration: BoxDecoration(
                    // 使用主题配置的头像阴影
                    boxShadow: avatarShadows,
                  ),
                  child: Image.asset(
                    widget.avatar!,
                    fit: BoxFit.cover,
                    width: 40,
                  ),
                ),
              ),
            ),
          // 使用text的方式可以匹配表情包的图片地址
          // 只有发送了特定的表情包文字后才能不为空，否则就是发送正常的文本
          ConstData.jineEmojiText[text] != null
              ? Padding(
                  padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                  child: Image.asset(ConstData.jineEmojiText[text]),
                )
              : Flexible(
                  child: Padding(
                  // 根据用户类型来处理气泡左右的宽度
                  padding: (widget.message.ownerType == OwnerType.receiver)
                      ? const EdgeInsets.fromLTRB(5, 5, 40, 5)
                      : const EdgeInsets.fromLTRB(40, 5, 5, 5),
                  child: BubbleWidget(
                    arrowDirection:
                        widget.message.ownerType == OwnerType.receiver
                            ? AxisDirection.left
                            : AxisDirection.right,
                    arrowOffset: 22,
                    arrowLength: 8,
                    arrowRadius: 4,
                    arrowWidth: 14,
                    padding: const EdgeInsets.all(12),
                    borderRadius: BorderRadius.circular(bubbleRadius),
                    backgroundColor:
                        widget.message.ownerType == OwnerType.receiver
                            ? receiverColor
                            : senderColor,
                    // 使用主题配置的气泡阴影
                    shadows: bubbleShadows,
                    contentBuilder: (context) {
                      // {{ AURA-X: Modify - 添加流式状态指示器支持. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
                      return _buildMessageContent(context, text, isStreaming);
                    },
                  ),
                ))
        ],
      ),
    );
  }

  // {{ AURA-X: Add - 构建消息内容，包含流式状态指示器. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  Widget _buildMessageContent(
      BuildContext context, String text, bool isStreaming) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // 主要内容
        Flexible(
          child: MarkdownBody(
            data: text,
            styleSheet: ThemeMarkdownStyle.getStyle(context, themeConfig!),
            selectable: false,
            builders: {
              'h1': H1Builder(themeConfig!),
            },
          ),
        ),

        // 流式状态指示器（仅在AI消息且正在流式传输时显示）
        if (isStreaming && widget.message.ownerType == OwnerType.receiver)
          _buildStreamingIndicator(context),
      ],
    );
  }

  // {{ AURA-X: Add - 构建流式状态指示器. Approval: 寸止(ID:2025-08-03T23:45:00+08:00). }}
  Widget _buildStreamingIndicator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0),
      child: AnimatedBuilder(
        animation: _cursorAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _cursorAnimation.value,
            child: Container(
              width: 2,
              height: 16,
              decoration: BoxDecoration(
                color: themeConfig!.receiverTextColor,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          );
        },
      ),
    );
  }
}
