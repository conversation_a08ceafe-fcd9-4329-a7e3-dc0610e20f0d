// {{ AURA-X: Add - 创建模型详细信息卡片组件. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../../../../ui/widgets/toast/toast.dart';
import '../shared/model_utils.dart';

class ModelDetailCard extends StatelessWidget {
  final dynamic model;
  final ProfileController controller;

  const ModelDetailCard({
    Key? key,
    required this.model,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final modelName = ModelUtils.getModelName(model);
    final modelDescription = ModelUtils.getModelDescription(model);
    final inputPrice = ModelUtils.getInputPrice(model);
    final outputPrice = ModelUtils.getOutputPrice(model);
    final priceTip = ModelUtils.getPriceTip(model);
    final tags = ModelUtils.getTags(model);
    final maxToken = ModelUtils.getMaxToken(model);

    return Obx(() => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.background,
                Theme.of(context).colorScheme.surface.withOpacity(0.5),
              ],
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(24, 20, 24, 100), // 底部留空间给导航器
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 模型头部区域
                _buildHeroSection(context, modelName),

                const SizedBox(height: 32),

                // 标签区域
                if (tags.isNotEmpty) ...[
                  _buildInfoCard(
                    context,
                    '特性标签',
                    Icons.label_outline,
                    ModelUtils.buildTagList(tags, compact: false, spacing: 8),
                  ),
                  const SizedBox(height: 20),
                ],

                // 价格信息
                _buildPriceCard(context, inputPrice, outputPrice, priceTip),

                const SizedBox(height: 20),

                // 技术规格
                _buildSpecCard(context, maxToken),

                const SizedBox(height: 20),

                // 模型描述
                _buildDescriptionCard(context, modelDescription),

                const SizedBox(height: 24),

                // 操作按钮
                _buildActionButton(context, modelName),
              ],
            ),
          ),
        ));
  }

  Widget _buildHeroSection(BuildContext context, String modelName) {
    final isSelected = ModelUtils.isCurrentModel(model, controller.currentModelName.value);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 模型图标
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.smart_toy,
              color: Theme.of(context).primaryColor,
              size: 40,
            ),
          ),

          const SizedBox(height: 16),

          // 模型名称
          Text(
            modelName,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // 状态标识
          if (isSelected)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: const [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 6),
                  Text(
                    '当前使用中',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context, String title, IconData icon, Widget content) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildPriceCard(BuildContext context, double inputPrice, double outputPrice, String priceTip) {
    return _buildInfoCard(
      context,
      '价格信息',
      Icons.attach_money,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Column(
          children: [
            if (inputPrice == 0.0 && outputPrice == 0.0)
              Row(
                children: [
                  Icon(Icons.free_breakfast, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    '免费使用',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              )
            else ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('输入价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text('${inputPrice.toStringAsFixed(2)} / 百万tokens'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('输出价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text('${outputPrice.toStringAsFixed(2)} / 百万tokens'),
                ],
              ),
            ],
            if (priceTip.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        priceTip,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSpecCard(BuildContext context, int maxToken) {
    return _buildInfoCard(
      context,
      '技术规格',
      Icons.memory,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '最大Token数',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    ModelUtils.formatNumber(maxToken),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.speed,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard(BuildContext context, String description) {
    return _buildInfoCard(
      context,
      '模型描述',
      Icons.description,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          description,
          style: TextStyle(
            fontSize: 15,
            height: 1.6,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, String modelName) {
    final isSelected = ModelUtils.isCurrentModel(model, controller.currentModelName.value);

    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: isSelected
          ? LinearGradient(
              colors: [Colors.grey.shade400, Colors.grey.shade500],
            )
          : LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
            ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: isSelected ? null : [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: isSelected ? null : () => _selectModel(context, modelName),
        icon: Icon(
          isSelected ? Icons.check_circle : Icons.touch_app,
          color: Colors.white,
        ),
        label: Text(
          isSelected ? '当前使用中' : '使用此模型',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
    );
  }

  void _selectModel(BuildContext context, String modelName) {
    controller.setCurrentModel(model);
    Toast.showSuccess('切换成功', '当前模型：$modelName');
  }
}
